@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes floatAnimation {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes pulseGlow {
  0% {
    box-shadow: 0 0 20px rgba(37, 114, 237, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(37, 114, 237, 0.4);
  }
  100% {
    box-shadow: 0 0 20px rgba(37, 114, 237, 0.2);
  }
}

.permission-ui-modal {
  .ant-modal-content {
    border-radius: 24px;
    padding: 0;
    overflow: hidden;
    background: url('../../../../../styles/images/frontend/auth-bg.jpg') no-repeat center center;
    background-size: cover;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  }

  .ant-modal-body {
    padding: 0;
    overflow: hidden;

  }

  .ant-modal-mask {
    animation: gradientAnimation 15s ease infinite;
    background-size: 200% 200%;
    backdrop-filter: blur(8px);
  }
}

.permission-ui-container {
  border-radius: 0;
  padding: 0 0 2.5rem 0; /* Increased bottom padding */
  max-width: 600px;
  width: 100%;
  transform: scale(1);
  transform-origin: center;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.permission-ui-illustration {
  width: 100%;
  max-width: 100%;
  margin: 15px 0 0 0; /* Added top margin to push image down */
  padding: 0;
  animation: floatAnimation 6s ease-in-out infinite;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
  border-radius: 0;
  display: block;
  object-fit: contain; /* Changed from cover to contain to prevent zooming */
  flex-shrink: 0;
  transform: translateY(15px); /* Removed scale, only moved image lower */
  height: auto;
  min-height: 180px; /* Decreased height */
  max-height: 200px; /* Decreased max-height */
  margin-bottom: 10px; /* Added bottom margin to push content down */
}

.permission-ui-title {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 1.5rem 0 0.75rem; /* Adjusted top margin to account for image position */
  letter-spacing: -0.5px;
  padding: 0;
  width: 100%;
  text-align: center;
}

.permission-ui-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 2rem; /* Increased bottom margin */
  line-height: 1.4;
  font-weight: 400;
  padding: 0;
  width: 100%;
  text-align: center;
}

.permission-ui-btn {
  font-family: "Inter", system-ui;
  font-size: 15px;
  border-radius: 8px;
  padding: 0.6rem 1.2rem;
  width: 100%;
  max-width: 280px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  position: relative;
  overflow: hidden;
  margin-bottom: 1rem; /* Increased spacing between buttons */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transition: 0.5s;
  }

  &:hover::before {
    left: 100%;
  }
}

.permission-ui-btn.btn-primary {
  background: #2572ED;
  color: #ffffff !important;
}

.permission-ui-btn.btn-primary:hover,
.permission-ui-btn.btn-primary:focus {
  background: #1a5fd9;
  color: #ffffff !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 114, 237, 0.2);
}

.permission-ui-btn.btn-outline-primary {
  border: 1px solid #2572ED;
  color: #2572ED;
  background: rgba(255, 255, 255, 0.95);
  font-weight: 500;
}

.permission-ui-btn.btn-outline-primary:hover,
.permission-ui-btn.btn-outline-primary:focus {
  background: rgba(37, 114, 237, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(37, 114, 237, 0.1);
}

.permission-ui-btn.btn-outline-secondary {
  border: 1px solid #6c757d;
  color: #6c757d;
  background: rgba(255, 255, 255, 0.95);
  font-weight: 500;
}

.permission-ui-btn.btn-outline-secondary:hover,
.permission-ui-btn.btn-outline-secondary:focus {
  background: rgba(108, 117, 125, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.1);
}

.permission-instructions {
  max-width: 400px;
  margin: 0 auto;

  .alert {
    border-radius: 8px;
    border: none;
    background: rgba(13, 202, 240, 0.1);
    color: #0c63e4;
    font-size: 14px;
    padding: 1rem;
    margin-bottom: 0.5rem;
  }

  .detailed-instructions {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);

    h6 {
      color: #333;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    ol {
      margin-bottom: 0;
      padding-left: 1.2rem;

      li {
        margin-bottom: 0.25rem;
        color: #555;
        line-height: 1.4;
      }
    }
  }
}

.permission-ui-link {
  color: #2572ED;
  font-size: 0.9rem;
  text-decoration: none !important; /* Ensure no underline appears */
  background: none;
  border: none;
  padding: 0.4rem 0.8rem;
  margin-top: 0.75rem; /* Increased top margin */
  font-family: "Inter", system-ui;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
  opacity: 0.85;

  /* Removed the ::after pseudo-element that created the underline */

  &:hover, &:focus {
    color: #1a5fd9;
    transform: translateY(-1px);
    opacity: 1;
    text-decoration: none !important; /* Ensure no underline appears on hover */
  }
}

@media (max-width: 768px) {
  .permission-ui-modal {
    .ant-modal-content {
      border-radius: 20px;
    }
  }

  .permission-ui-container {
    padding: 0 0 1.5rem 0;
    border-radius: 0;
    max-width: 100%;
    transform: scale(1);
    transform-origin: center;
    justify-content: center;
  }

  .permission-ui-title {
    font-size: 1.25rem;
    margin: 0.75rem 0 0.5rem; /* Adjusted top margin for mobile */
    width: 90%;
  }

  .permission-ui-desc {
    font-size: 0.95rem;
    width: 90%;
  }

  .permission-ui-illustration {
    width: 100%;
    max-width: 100%;
    margin: 15px 0 0 0; /* Added top margin to push image down */
    padding: 0;
    border-radius: 0;
    transform: translateY(15px); /* Removed scale, only moved image lower */
    min-height: 160px; /* Decreased height for mobile */
    max-height: 180px; /* Decreased max-height for mobile */
    margin-bottom: 10px; /* Added bottom margin to push content down */
  }

  .permission-ui-btn {
    font-size: 15px;
    padding: 0.6rem 1rem;
    max-width: 90%;
  }

  .permission-ui-link {
    font-size: 0.95rem;
    margin-top: 0.5rem;
  }
}

@media (max-width: 480px) {
  .permission-ui-modal {
    .ant-modal-content {
      border-radius: 16px;
    }
  }

  .permission-ui-container {
    padding: 0 0 1.2rem 0;
    border-radius: 0;
    transform: scale(1);
    transform-origin: center;
    justify-content: center;
  }

  .permission-ui-title {
    font-size: 1.1rem;
    margin: 0.5rem 0 0.4rem; /* Adjusted top margin for smallest screens */
    width: 90%;
  }

  .permission-ui-desc {
    font-size: 0.9rem;
    margin-bottom: 1.2rem;
    width: 90%;
  }

  .permission-ui-illustration {
    width: 100%;
    max-width: 100%;
    margin: 12px 0 0 0; /* Added top margin to push image down (slightly less for small screens) */
    padding: 0;
    border-radius: 0;
    transform: translateY(15px); /* Removed scale, only moved image lower */
    min-height: 140px; /* Decreased height for smallest screens */
    max-height: 160px; /* Decreased max-height for smallest screens */
    margin-bottom: 10px; /* Added bottom margin to push content down */
  }

  .permission-ui-btn {
    font-size: 14px;
    padding: 0.5rem 1rem;
    max-width: 90%;
  }

  .permission-ui-link {
    font-size: 0.85rem;
    margin-top: 0.25rem;
  }
}